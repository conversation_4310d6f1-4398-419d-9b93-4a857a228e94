import { useState } from 'react';
import {
  User,
  LogOut,
  Plus,
  Eye,
  Trash2,
  Clock,
  CheckCircle,
  AlertCircle,
  Activity,
  Calendar,
  BarChart3,
  ChevronLeft,
  ChevronRight,
  FileText
} from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import apiService from '../../services/apiService';
import useDashboard from '../../hooks/useDashboard';
import { useNotifications } from '../../hooks/useNotifications';
import DeleteResultModal from './DeleteResultModal';
import ConnectionStatus from './ConnectionStatus';





export default function ImprovedDashboard() {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [deleteModal, setDeleteModal] = useState({ isOpen: false, result: null });

  // WebSocket notifications
  const { notifications, clearNotification } = useNotifications({
    onAnalysisComplete: () => {
      // Refresh dashboard data when analysis completes
      refreshData();
    },
    onAnalysisFailed: () => {
      // Could show additional error handling here
      refreshData(); // Refresh to update failed status
    }
  });

  const {
    data: { results, overview, pagination },
    loading: isLoading,
    error,
    deleteResult,
    refreshData,
    setError
  } = useDashboard(currentPage, 10);



  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status) => {
    const configs = {
      completed: {
        icon: CheckCircle,
        classes: 'bg-green-100 text-green-800 border-green-200',
        label: 'Completed'
      },
      processing: {
        icon: Clock,
        classes: 'bg-blue-100 text-blue-800 border-blue-200',
        label: 'Processing'
      },
      failed: {
        icon: AlertCircle,
        classes: 'bg-red-100 text-red-800 border-red-200',
        label: 'Failed'
      }
    };

    const config = configs[status] || configs.completed;
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center space-x-1 px-2 py-1 text-xs font-medium rounded-full border ${config.classes}`}>
        <Icon className="h-3 w-3" />
        <span>{config.label}</span>
      </span>
    );
  };

  const handleViewResult = (resultId) => {
    navigate(`/results/${resultId}`);
  };

  const handleDeleteResult = (result) => {
    setDeleteModal({ isOpen: true, result });
  };

  const handleDeleteConfirmed = async (deletedResultId) => {
    const result = await deleteResult(deletedResultId);
    if (result.success) {
      setDeleteModal({ isOpen: false, result: null });
    } else {
      setError(result.error);
    }
  };

  const handleCloseDeleteModal = () => {
    setDeleteModal({ isOpen: false, result: null });
  };

  const handleLogout = async () => {
    try {
      await apiService.logout();
    } catch (err) {
      // Logout error handled silently
    } finally {
      logout();
      navigate('/auth');
    }
  };

  const handleNewAssessment = () => {
    navigate('/assessment');
  };

  const handleProfile = () => {
    navigate('/profile');
  };

  // Remove loading screen - show dashboard immediately with skeleton if needed

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-indigo-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-indigo-100 rounded-xl">
                <BarChart3 className="h-8 w-8 text-indigo-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
                <p className="text-sm text-gray-600 flex items-center space-x-1">
                  <span>Welcome back,</span>
                  <span className="font-medium text-indigo-600">{user?.email || 'User'}</span>
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <ConnectionStatus />

              {/* Debug Assessment Button - Only show in development */}
              {import.meta.env.DEV && (
                <button
                  onClick={() => navigate('/assessment?debug=true')}
                  className="flex items-center space-x-2 px-4 py-2 text-white bg-orange-500 rounded-lg hover:bg-orange-600 transition-colors"
                  title="Debug Assessment (Auto-filled)"
                >
                  <span className="text-sm">🔧</span>
                  <span className="hidden sm:inline text-sm">Debug Assessment</span>
                </button>
              )}

              <button
                onClick={handleProfile}
                className="flex items-center space-x-2 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <User className="h-4 w-4" />
                <span className="hidden sm:inline">Profile</span>
              </button>

              <button
                onClick={handleLogout}
                className="flex items-center space-x-2 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <LogOut className="h-4 w-4" />
                <span className="hidden sm:inline">Logout</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Notifications */}
      {notifications.length > 0 && (
        <div className="fixed top-4 right-4 z-50 space-y-2">
          {notifications.slice(0, 3).map((notification) => (
            <div
              key={notification.id}
              className={`max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden transform transition-all duration-300 ${
                notification.type === 'success' ? 'border-l-4 border-green-500' : 'border-l-4 border-red-500'
              }`}
            >
              <div className="p-4">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    {notification.type === 'success' ? (
                      <CheckCircle className="h-6 w-6 text-green-400" />
                    ) : (
                      <AlertCircle className="h-6 w-6 text-red-400" />
                    )}
                  </div>
                  <div className="ml-3 w-0 flex-1 pt-0.5">
                    <p className="text-sm font-medium text-gray-900">{notification.title}</p>
                    <p className="mt-1 text-sm text-gray-500">{notification.message}</p>
                  </div>
                  <div className="ml-4 flex-shrink-0 flex">
                    <button
                      className="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                      onClick={() => clearNotification(notification.id)}
                    >
                      <span className="sr-only">Close</span>
                      <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards - Responsive Grid Layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Total Assessments */}
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200 hover:scale-105">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-indigo-100 rounded-lg">
                <BarChart3 className="h-6 w-6 text-indigo-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Total Assessments</p>
                {isLoading ? (
                  <div className="h-8 bg-gray-200 rounded w-16 mt-1 animate-pulse"></div>
                ) : (
                  <p className="text-2xl font-bold text-gray-900 mt-1">{overview.summary?.total_assessments || 0}</p>
                )}
              </div>
            </div>
          </div>

          {/* This Month */}
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200 hover:scale-105">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-green-100 rounded-lg">
                <Calendar className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">This Month</p>
                {isLoading ? (
                  <div className="h-8 bg-gray-200 rounded w-16 mt-1 animate-pulse"></div>
                ) : (
                  <p className="text-2xl font-bold text-green-600 mt-1">{overview.summary?.this_month || 0}</p>
                )}
              </div>
            </div>
          </div>

          {/* Success Rate */}
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200 hover:scale-105">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-blue-100 rounded-lg">
                <CheckCircle className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Success Rate</p>
                {isLoading ? (
                  <div className="h-8 bg-gray-200 rounded w-16 mt-1 animate-pulse"></div>
                ) : (
                  <p className="text-2xl font-bold text-blue-600 mt-1">{Math.round((overview.summary?.success_rate || 0) * 100)}%</p>
                )}
              </div>
            </div>
          </div>

          {/* Unique Archetypes */}
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200 hover:scale-105">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-purple-100 rounded-lg">
                <Activity className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Unique Archetypes</p>
                {isLoading ? (
                  <div className="h-8 bg-gray-200 rounded w-16 mt-1 animate-pulse"></div>
                ) : (
                  <p className="text-2xl font-bold text-purple-600 mt-1">{overview.archetype_summary?.unique_archetypes || 0}</p>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Archetype Summary */}
        {!isLoading && overview.archetype_summary?.most_common && (
          <div className="bg-white shadow-sm rounded-xl border border-gray-100 mb-8">
            <div className="px-6 py-4 border-b border-gray-100">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-indigo-100 rounded-lg">
                  <Activity className="h-5 w-5 text-indigo-600" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-gray-900">Archetype Summary</h2>
                  <p className="text-sm text-gray-500">Your personality archetype insights</p>
                </div>
              </div>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="text-center">
                  <p className="text-sm font-medium text-gray-600">Most Common</p>
                  <p className="text-lg font-bold text-indigo-600 mt-1">{overview.archetype_summary.most_common}</p>
                  <p className="text-xs text-gray-500">({overview.archetype_summary.frequency} times)</p>
                </div>

                <div className="text-center">
                  <p className="text-sm font-medium text-gray-600">Latest Result</p>
                  <p className="text-lg font-bold text-green-600 mt-1">{overview.archetype_summary.last_archetype}</p>
                </div>

                <div className="text-center">
                  <p className="text-sm font-medium text-gray-600">Unique Types</p>
                  <p className="text-lg font-bold text-purple-600 mt-1">{overview.archetype_summary.unique_archetypes}</p>
                </div>

                <div className="text-center">
                  <p className="text-sm font-medium text-gray-600">Trend</p>
                  <p className="text-lg font-bold text-blue-600 mt-1 capitalize">{overview.archetype_summary.archetype_trend}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-4 mb-6">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-red-500" />
              <div>
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Assessment History */}
        <div className="bg-white shadow-sm rounded-xl border border-gray-100">
          <div className="px-6 py-4 border-b border-gray-100">
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gray-100 rounded-lg">
                  <FileText className="h-5 w-5 text-gray-600" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">Assessment History</h2>
                  <p className="text-sm text-gray-500">Track your assessment progress and results</p>
                </div>
              </div>
              
              <button
                onClick={handleNewAssessment}
                className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors shadow-sm"
              >
                <Plus className="h-4 w-4" />
                <span>New Assessment</span>
              </button>
            </div>
          </div>
          
          {results.length === 0 ? (
            <div className="text-center py-16">
              <div className="p-4 bg-gray-100 rounded-full w-16 h-16 mx-auto mb-4">
                <FileText className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No assessments yet</h3>
              <p className="text-gray-500 mb-6">Your assessment results will appear here once completed.</p>
              <button
                onClick={handleNewAssessment}
                className="flex items-center space-x-2 px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors mx-auto"
              >
                <Plus className="h-4 w-4" />
                <span>Start Your First Assessment</span>
              </button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-100">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4" />
                        <span>Date & Time</span>
                      </div>
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      <div className="flex items-center space-x-1">
                        <Activity className="h-4 w-4" />
                        <span>Status</span>
                      </div>
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-100">
                  {results.map((result) => (
                    <tr key={result.id} className="hover:bg-gray-50 transition-colors group">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {formatDate(result.created_at)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(result.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-3">
                          {result.status === 'completed' ? (
                            <button
                              onClick={() => handleViewResult(result.id)}
                              className="flex items-center space-x-1 text-indigo-600 hover:text-indigo-800 font-medium text-sm transition-colors hover:bg-indigo-50 px-2 py-1 rounded"
                            >
                              <Eye className="h-4 w-4" />
                              <span className="hidden sm:inline">View Results</span>
                              <span className="sm:hidden">View</span>
                            </button>
                          ) : result.status === 'processing' ? (
                            <div className="flex items-center space-x-1 text-blue-600">
                              <div className="h-4 w-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                              <span className="text-sm hidden sm:inline">Processing...</span>
                              <span className="text-sm sm:hidden">Processing</span>
                            </div>
                          ) : (
                            <span className="text-sm text-gray-500">Failed</span>
                          )}

                          <button
                            onClick={() => handleDeleteResult(result)}
                            className="flex items-center space-x-1 text-red-600 hover:text-red-800 font-medium text-sm transition-colors hover:bg-red-50 px-2 py-1 rounded"
                          >
                            <Trash2 className="h-4 w-4" />
                            <span className="hidden sm:inline">Delete</span>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* Pagination */}
          {pagination && pagination.totalPages > 1 && (
            <div className="px-6 py-4 border-t border-gray-100 bg-gray-50">
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-600">
                  Showing {((currentPage - 1) * pagination.limit) + 1} to {Math.min(currentPage * pagination.limit, pagination.total)} of {pagination.total} results
                </div>
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => {
                      const newPage = Math.max(currentPage - 1, 1);
                      setCurrentPage(newPage);
                    }}
                    disabled={currentPage === 1}
                    className="flex items-center space-x-1 px-3 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    <ChevronLeft className="h-4 w-4" />
                    <span>Previous</span>
                  </button>

                  <div className="flex items-center space-x-1">
                    {Array.from({ length: Math.min(pagination.totalPages, 5) }, (_, i) => (
                      <button
                        key={i + 1}
                        onClick={() => {
                          setCurrentPage(i + 1);
                        }}
                        className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                          currentPage === i + 1
                            ? 'bg-indigo-600 text-white'
                            : 'text-gray-600 bg-white border border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        {i + 1}
                      </button>
                    ))}
                  </div>

                  <button
                    onClick={() => {
                      const newPage = Math.min(currentPage + 1, pagination.totalPages);
                      setCurrentPage(newPage);
                    }}
                    disabled={currentPage === pagination.totalPages}
                    className="flex items-center space-x-1 px-3 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    <span>Next</span>
                    <ChevronRight className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Delete Result Modal */}
      <DeleteResultModal
        isOpen={deleteModal.isOpen}
        onClose={handleCloseDeleteModal}
        result={deleteModal.result}
        onDeleted={handleDeleteConfirmed}
      />
    </div>
  );
}