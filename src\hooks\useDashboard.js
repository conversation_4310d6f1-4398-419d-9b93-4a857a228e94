import { useState, useEffect, useCallback, useRef } from 'react';
import apiService from '../services/apiService';

/**
 * Custom hook for managing dashboard data
 * Handles fetching overview stats and results data
 */
export const useDashboard = (currentPage = 1, limit = 10) => {
  // Use ref to track if component is mounted to prevent state updates after unmount
  const isMountedRef = useRef(true);

  const [data, setData] = useState({
    results: [],
    overview: {
      summary: {
        total_assessments: 0,
        this_month: 0,
        success_rate: 0
      },
      recent_results: [],
      archetype_summary: {
        most_common: '',
        frequency: 0,
        last_archetype: '',
        unique_archetypes: 0,
        archetype_trend: ''
      }
    },
    pagination: {
      page: 1,
      limit: 10,
      total: 0,
      totalPages: 0
    }
  });

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const fetchDashboardData = useCallback(async () => {
    // Prevent API calls if component is unmounted (helps with StrictMode double calls)
    if (!isMountedRef.current) return;

    try {
      setLoading(true);
      setError('');

      // Fetch overview and results data in parallel
      const [overviewResponse, resultsResponse] = await Promise.all([
        apiService.getStatsOverview(),
        apiService.getResults({ page: currentPage, limit })
      ]);

      // Check again if component is still mounted before updating state
      if (!isMountedRef.current) return;

      const newData = { ...data };

      if (overviewResponse.success) {
        newData.overview = overviewResponse.data;
      }

      if (resultsResponse.success) {
        newData.results = resultsResponse.data.results || [];
        newData.pagination = resultsResponse.data.pagination || {
          page: currentPage,
          limit,
          total: 0,
          totalPages: 0
        };
      }

      setData(newData);

    } catch (err) {
      // Only update error state if component is still mounted
      if (isMountedRef.current) {
        setError(err.response?.data?.message || 'Failed to load dashboard data');
      }
    } finally {
      // Only update loading state if component is still mounted
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  }, [currentPage, limit]);

  const deleteResult = useCallback(async (resultId) => {
    try {
      const response = await apiService.deleteResult(resultId);
      if (response.success) {
        // Remove from local state
        setData(prevData => ({
          ...prevData,
          results: prevData.results.filter(result => result.id !== resultId)
        }));
        
        // Refresh data to get updated stats
        await fetchDashboardData();
        return { success: true };
      }
    } catch (err) {
      const errorMessage = err.response?.data?.message || 'Failed to delete result';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  }, [fetchDashboardData]);

  const refreshData = useCallback(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  useEffect(() => {
    fetchDashboardData();

    // Cleanup function to mark component as unmounted
    return () => {
      isMountedRef.current = false;
    };
  }, [fetchDashboardData]);

  return {
    data,
    loading,
    error,
    deleteResult,
    refreshData,
    setError
  };
};

export default useDashboard;
